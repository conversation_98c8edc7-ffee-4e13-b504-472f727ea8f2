// @vue/component

const cityOptions = ["上海", "北京", "广州", "深圳"];

export default {
  name: 'LeftFilter',

  components: {},

  mixins: [],

  props: {
    // 需要筛选的api_name ,仅支持input 和 selected
    filter_fields: {
      type: Array,
      default: () => []
    },
    objectFields: { 
      type: Object,
      default: () => {}
    }
  },

  data () {
    return {
      // 控制各个筛选项的展开/收起状态
      expandedSections: {},
      // 存储各个筛选项的选中值
      selectedValues: {},

      checkedCities: [],
      cities: cityOptions
    }
  },

  computed: {},

  watch: {
    // 监听 filter_fields 变化，初始化展开状态
    filter_fields: {
      handler(newFields) {
        this.initExpandedSections(newFields);
        this.initSelectedValues(newFields);
      },
      immediate: true
    }
  },

  created () {
    console.log('this.filter_fields', this.filter_fields);
    this.initExpandedSections(this.filter_fields);
    this.initSelectedValues(this.filter_fields);
  },

  methods: {

    handleCheckedCitiesChange(value) {
      console.log('handleCheckedCitiesChange value:', value);
    },

    /**
     * 初始化展开状态，默认第一个筛选项展开
     */
    initExpandedSections(fields) {
      const expandedSections = {};
      fields.forEach((field, index) => {
        expandedSections[field] = index === 0; // 默认第一个展开
      });
      this.expandedSections = expandedSections;
    },

    /**
     * 初始化选中值
     */
    initSelectedValues(fields) {
      fields.forEach(field => {
        const fieldType = this.getFieldType(field);
        if (field === 'commodity_label') {
          debugger;
        }
        if (fieldType === 'select_many') {
          this.$set(this.selectedValues, field, []); // 保证响应式
        } else {
          this.$set(this.selectedValues, field, '');
        }
      });
    },

    /**
     * 切换筛选项的展开/收起状态
     */
    toggleSection(fieldName) {
      this.$set(this.expandedSections, fieldName, !this.expandedSections[fieldName]);
    },

    /**
     * 获取字段的显示标签
     */
    getFieldLabel(fieldName) {
      const field = this.objectFields[fieldName];
      return field ? field.label : fieldName;
    },

    /**
     * 获取字段类型
     */
    getFieldType(fieldName) {
      const field = this.objectFields[fieldName];
      return field ? field.type : 'input';
    },

    /**
     * 获取字段的选项列表
     */
    getFieldOptions(fieldName) {
      const field = this.objectFields[fieldName];
      return field && field.options ? field.options : [];
    },

    /**
     * 处理筛选项值变化
     */
    onFilterChange(fieldName, value) {
      this.$set(this.selectedValues, fieldName, value);
      console.log('onFilterChange this.selectedValues:', this.selectedValues);
      // 触发筛选变化事件，向父组件传递筛选数据
      this.$emit('filter-change', {
        field: fieldName,
        value: value,
        allFilters: this.selectedValues
      });
    },

    /**
     * 获取当前所有筛选条件
     */
    getFilterValues() {
      return this.selectedValues;
    },

    /**
     * 重置所有筛选条件
     */
    resetFilters() {
      this.initSelectedValues(this.filter_fields);
      this.$emit('filter-change', {
        field: null,
        value: null,
        allFilters: this.selectedValues
      });
    },

    /**
     * 设置筛选值（外部调用）
     */
    setFilterValues(values) {
      Object.keys(values).forEach(field => {
        this.$set(this.selectedValues, field, values[field]);
      });
    }
  }
}