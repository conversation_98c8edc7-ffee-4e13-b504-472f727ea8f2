<div class="dht-isolated-mall-shop-list">
  <div class="layout-left" v-if="filter_layout == '2'">
      <left-filter v-if="firstDataLoad" :filters="filters" :filter_fields="filter_fields" :objectFields="objectFields" @filter-change="handleFilterChange"/>
  </div>
  <div class="layout-right">
    <!-- 顶部筛选 -->
     <div class="filter-sort-wrap">
        <!-- <top-filter :filter_fields="filter_fields" :objectFields="objectFields" /> -->
    </div>

    <product-list
      v-show="firstDataLoad"
      :list="listData"
      :options="listOptions"
      @collectChange="collectChange">
    </product-list>
    <div class="dht-shop-pagination">
      <fx-pagination
        v-show="total > 0"
        background
        layout="prev, pager, next"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @current-change="paginationChange">
      </fx-pagination>
    </div>
  </div>
    
  <loading class="dht-absolute-center" v-show="isLoading"></loading>
</div>


