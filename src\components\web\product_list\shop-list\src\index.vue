<template>
  <div class="dhtbiz-shop-list">
    <shopList :category-node="category" :filters="filters" :filter_fields="filter_fields" :filter_layout="filter_layout" :card_main_info="card_main_info" :sort_fields="sort_fields" @filter-change="handleFilterChange"></shopList>
  </div>
</template>

<script>
export default {
  name: 'dht_web_product_list_shop_list',
  components: {
    shopList: () => Fx.getBizComponent('dhtbiz', 'mallShopList').then(res => res())    
  },
  props: {
    dhtPageData: {
      type: Object,
      default: () => ({})
    },
    dhtContainerApi: {
      type: Object,
      default: () => ({})
    },
    filter_fields: {
      type: Array,
      default: () => []
    },
    filter_layout: {
      type: Object,
      default: () => ({})
    },
    card_main_info: {
      type: Object,
      default: () => ({})
    },
    sort_fields: {  
      type: Array,
      default: () => []
    }
  },
  computed: {
    category() {
      return this.dhtPageData?.category;
    },
    filters() {
      return this.dhtPageData?.filters || [];
    }
  },
  methods: {
    handleFilterChange(values) {
      const { allFilters } = values;      
      this.$emit('filter-change', allFilters);
    }
  },
  created() {
    // this.initTable();
  },
  beforeDestroy() {
  },
};
</script>

<style lang="less" scoped>
.dhtbiz-shop-list {
  width: 100%;
}
</style> 