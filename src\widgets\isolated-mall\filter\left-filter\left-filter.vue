<template>
  <div class="left-filter">
    <!--for debug  <pre>{{ selectedValues }}</pre> -->

    <div 
      v-for="filterField in filter_fields" 
      :key="filterField"
      class="filter-section"
    >
      <div 
        class="filter-header" 
        @click="toggleSection(filterField)"
      >
        <span class="filter-title">{{ getFieldLabel(filterField) }}</span>
        <span 
          class="expand-icon fx-icon-arrow-down"

          :class="{ 'expanded': expandedSections[filterField] }"
        >
        </span>
      </div>
      
      <div 
        v-show="expandedSections[filterField]"
        class="filter-content"
      >
        <!-- 单选筛选项 -->
        <fx-radio-group 
          v-if="getFieldType(filterField) === 'select_one'"
          v-model="selectedValues[filterField]"
          @change="onFilterChange(filterField, $event)"
        >
          <fx-radio 
            v-for="option in getFieldOptions(filterField)"
            :key="option.value"
            :label="option.value"
            class="filter-option"
          >
            {{ option.label }}
          </fx-radio>
        </fx-radio-group>
        
        <!-- 多选筛选项 -->
        <fx-checkbox-group 
          v-else-if="getFieldType(filterField) === 'select_many'"
          v-model="selectedValues[filterField]"
          @change="onFilterChange(filterField, $event)"
        >
          <fx-checkbox 
            v-for="option in getFieldOptions(filterField)"
            :key="option.value"
            :label="option.value"
            class="filter-option"
          >
            {{ option.label }}
          </fx-checkbox>
        </fx-checkbox-group>
        
        <!-- 字段筛选 -->
        <fx-input
          v-else
          v-model="selectedValues[filterField]"
          :placeholder="`请输入${getFieldLabel(filterField)}`"
          @input="onFilterChange(filterField, $event)"
          class="filter-input"
        ></fx-input>
      </div>
    </div>
  </div>
</template>

<script src="./_left-filter.js"></script>
<style src="./_left-filter.less" lang="less" scoped></style>
