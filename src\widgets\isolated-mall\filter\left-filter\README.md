# 左侧筛选组件 (LeftFilter)

一个支持展开/收起的筛选组件，支持单选、多选和输入框筛选功能。

## 功能特性

- ✅ 支持展开/收起筛选项
- ✅ 支持单选筛选 (`select_one`)
- ✅ 支持多选筛选 (`select_many`)
- ✅ 支持输入框筛选 (`input`)
- ✅ 响应式设计
- ✅ 自定义样式

## 使用方法

### 基本用法

```vue
<template>
  <left-filter
    :filter_fields="filterFields"
    :objectFields="objectFields"
    @filter-change="onFilterChange"
    ref="leftFilter"
  ></left-filter>
</template>

<script>
import LeftFilter from './left-filter.vue'

export default {
  components: {
    LeftFilter
  },
  
  data() {
    return {
      // 需要筛选的字段列表
      filterFields: ['surface_treatment', 'memory', 'keyword_search'],
      
      // 字段配置对象
      objectFields: {
        surface_treatment: {
          label: '表面处理',
          type: 'select_one',
          options: [
            { label: '表面处理方式一', value: '1' },
            { label: '表面处理方式二', value: '2' }
          ]
        },
        memory: {
          label: '内存',
          type: 'select_many',
          options: [
            { label: '128G', value: '128' },
            { label: '256G', value: '256' }
          ]
        },
        keyword_search: {
          label: '关键词搜索',
          type: 'input'
        }
      }
    }
  },
  
  methods: {
    onFilterChange(filterData) {
      console.log('筛选变化:', filterData);
    }
  }
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| filter_fields | 需要筛选的字段名称数组 | Array | [] |
| objectFields | 字段配置对象 | Object | {} |

### objectFields 配置说明

每个字段的配置格式：

```javascript
{
  fieldName: {
    label: '字段显示名称',
    type: '字段类型', // 'select_one' | 'select_many' | 'input'
    options: [ // 仅当 type 为 'select_one' 或 'select_many' 时需要
      { label: '选项显示名称', value: '选项值' }
    ]
  }
}
```

#### 字段类型说明

- `select_one`: 单选，使用 `fx-radio-group` 和 `fx-radio` 实现
- `select_many`: 多选，使用 `fx-checkbox-group` 和 `fx-checkbox` 实现
- `input`: 输入框，使用 `fx-input` 实现

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| filter-change | 筛选条件变化时触发 | `{ field, value, allFilters }` |

### filter-change 事件参数说明

```javascript
{
  field: '变化的字段名',
  value: '变化的值',
  allFilters: '所有筛选条件的对象'
}
```

## Methods

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| getFilterValues | 获取当前所有筛选条件 | - | Object |
| resetFilters | 重置所有筛选条件 | - | - |
| setFilterValues | 设置筛选值 | values: Object | - |

### 方法使用示例

```javascript
// 获取筛选值
const filterValues = this.$refs.leftFilter.getFilterValues();

// 重置筛选
this.$refs.leftFilter.resetFilters();

// 设置筛选值
this.$refs.leftFilter.setFilterValues({
  surface_treatment: '1',
  memory: ['128', '256']
});
```

## 样式定制

组件使用 Less 编写样式，支持以下 CSS 变量定制：

```less
.left-filter {
  // 主要颜色
  --primary-color: #409eff;
  
  // 文字颜色
  --text-color: #333;
  --text-color-secondary: #666;
  --text-color-placeholder: #999;
  
  // 边框颜色
  --border-color: #dcdfe6;
  --border-color-light: #f0f0f0;
  
  // 背景颜色
  --background-color: #fff;
  --background-color-hover: #f5f5f5;
}
```

## 完整示例

参考 `example.vue` 文件查看完整的使用示例。

## 注意事项

1. 确保项目中已引入 `@fxui-doc` 组件库
2. 组件依赖 Vue 2.6+ 版本
3. 单选字段的值为字符串，多选字段的值为数组
4. 默认第一个筛选项会展开显示