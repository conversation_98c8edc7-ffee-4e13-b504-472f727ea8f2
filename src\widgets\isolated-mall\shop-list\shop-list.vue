<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import ProductList from "../../product-list/product-list.vue";
import { Product } from "@/widgets/types";
import { BomService } from "@/widgets/services/BomService";
import { UnitService } from "@/widgets/unit-selector/UnitService";
import Loading from '../../loading/loading.vue';

import LeftFilter from '../filter/left-filter/left-filter.vue';

@Component({
  name: 'ShopList',
  components: {
    ProductList,
    Loading,
    LeftFilter,
  }
})
export default class ShopList extends Vue {
  @Prop({default: []}) readonly filters!: Array<any>;
  @Prop({default: null}) readonly categoryNode!: any;
  @Prop({default: []}) readonly filter_fields!: any[];
  @Prop({default: '2'}) readonly filter_layout!: string;

  listData: Product[] = [];
  listOptions: any = {
    precision: 2,
    isLazyImg: false,
    isShowStock: this.isShowStock,
    visibleType: '2'
  }
  objectFields: any = {};

  commodityOptions: any[] = [];

  pageSize = 8;
  total = 0;
  currentPage = 1;

  isLoading = true;
  firstDataLoad = false;

  get isSpuMode() {
    return (window as any).$dht.config.sail.isSpuMode;
  }

  get isBomEnable() {
    return (window as any).$dht.config.bom.isEnable;
  }

  get isShowStock() {
    const { isEnable, visibleType } = (window as any).$dht.config.inventory;
    return isEnable && visibleType !== '1';
  }

  // 筛选值变化, 触发重新获取数据
  @Watch('filters')
  onFiltersChange(value: any) {    
    this.currentPage = 1;
    this.getListData();
  }

  @Watch('categoryNode')
  onCategoryChange(node: any) {
    this.currentPage = 1;
    this.getListData();
  }

  // 向上传递筛选值变化, 更新容器中的筛选值
  handleFilterChange(value: any) {
    this.$emit('filter-change', value);
  }

  created() {
    this.initPageSize();
    this.getListData();
  }

  paginationChange(value: number) {
    this.currentPage = value;
    this.getListData();
  }

  async getListData() {
    const params = await this._getParams();
    return new Promise((resolve, reject) => {
      this.isLoading = true;
      CRM.util.FHHApi({
        url: `/EM1HNCRM/API/v1/object/${this.isSpuMode ? 'SPUObj' : 'ProductObj'}/controller/RelatedList`,
        data: params,
        success: (res: any) => {          
          if (res.Result.StatusCode === 0) {
            const Value = res.Value;
            this.total = Value.total;
            this.mergeExtToDescribe(Value.objectDescribe, Value.objectDescribeExt || {});
            this.objectFields = Value.objectDescribe?.fields || {};
            this.formatDataAsync({ data: Value.dataList }).then( r => {
              Value.dataList = r.data;
              // res.multiUnitOptionsList = r.multiUnitOptionsList;
              const productList = this.formatCommodity(Value || {});
              this.listData = this.convertSpuList(productList);
            });
            resolve(Value);
          } else {
            CRM.util.error(res.Result.FailureMessage);
            reject(res);
          }
          this.isLoading = false;
          if (!this.firstDataLoad) {
            this.firstDataLoad = !this.firstDataLoad;
          }
        },
        error: (err: any) => {
          this.isLoading = false;
          reject(err);
        },
      }, {
        errorAlertModel: 1
      });
    });
  }

  async _getParams() {
    const search_query_info = await this._getFilters();
    const params = {
      associated_object_describe_api_name: this.isSpuMode ? "SPUObj" : 'ProductObj',
      associated_object_field_related_list_name: this.isSpuMode ? 'SPUObj_salesorderproduct_list' : 'salesorderproduct_product_list',
      ignore_scene_record_type: true,
      include_describe: true,
      master_data: {
        object_describe_api_name: 'SalesOrderObj',
        mc_currency: (window as any).$dht.config.currency.currencyCode,
      },
      object_data: _.extend({
        object_describe_api_name: 'SalesOrderProductObj',
        is_real_lookup: false,
      }),
      search_query_info,
      search_template_id: ''
    }

    // 如果有设置可售范围参数，则放入入参
    let availableRangeFilterData = (window as any).$dht.getMainFilterData();
    if (!_.isEmpty(availableRangeFilterData)) {
      (params.master_data as any)[availableRangeFilterData.apiname] = availableRangeFilterData.value
    }

    if ((window as any).$dht.config.sail.isOpenMultiLevelOrder) {
      (window as any).$dht.getService('distribution').formatProductParams(params);
    }

    return params;
  }

  /**
   * 根据屏幕宽度初始化分页大小
   */
  initPageSize() {
    const availableScreenWidth = (window as any).screen.availWidth || 1440;
    if (availableScreenWidth < 1680) {
      this.pageSize = 8;
    } else if (availableScreenWidth < 2500) {
      this.pageSize = 10;
    } else {
      this.pageSize = 14;
    }
  }

  async _getFilters() {
    const wheres = this._getWheres();
    const orders = await this._getOrders();
    const search_query_info: any = {
      offset: (this.currentPage - 1) * this.pageSize,
      limit: this.pageSize,
      filters: [],
      wheres,
      orders
    };
    try {
      // 分类赋值
      if (this.categoryNode) {
        search_query_info.filters.push({
          field_name: 'mall_category_id',
          field_values: [this.categoryNode.id],
          operator: 'HASANYOF',
        });
      }    

      // 筛选赋值
      const filters = JSON.parse(JSON.stringify(this.filters));
      if (filters && filters.length > 0) {
        search_query_info.filters = search_query_info.filters.concat(filters);
      }

      return JSON.stringify(search_query_info);
    } catch (error) {
      // console.error('shop-list _getFilters error:', error);
      return JSON.stringify(search_query_info);
    }    
  }

  _getOrders() {
    const params = {
      apiname: this.isSpuMode ? 'SPUObj' : 'ProductObj',
      check_edit_permission: false,
      include_layout: true,
      layout_by_template: true,
      layout_type: 'list',
      list_type: 'selected',
    };
    return (window as any).$dht.services.listheader.getListOrderFilter(params);
  }

  _getWheres() {
    // 取缓存描述product_id字段里的wheres，如果有，直接使用
    let desCache = (window as any).$dht.services.meta.getDescribeAndLayoutInCache({
      object_api_name: 'SalesOrderProductObj',
      record_type: 'default__c',
    });
    let fields = desCache && desCache.objectDescribe && desCache.objectDescribe.fields;
    let wheres = fields && fields.product_id && fields.product_id.wheres;
    if (wheres && wheres.length) {
      return wheres;
    }
    // 否则给默认wheres
    let defaultWheres: any[] = [{
      connector: 'OR',
      filters: [{
        field_name: 'product_status',
        field_values: ['1'],
        operator: 'EQ',
        value_type: 0,
      }]
    }];

    if (this.isBomEnable) {
      defaultWheres[0].filters.push({
        operator: 'EQ',
        field_name: 'is_saleable',
        field_values: [true]
      });
    }
    return defaultWheres;
  }
  
  mergeExtToDescribe(describe: any, describeExt: any) {
      if (describe && describeExt) {
        _.each(describeExt.fields, function (fieldDesExt: any, key: any) {
          let fieldDes = describe.fields[key];
          describe.fields[key] = Object.assign({}, fieldDes, fieldDesExt);
        });
        //对象级map合并
        let fields = describe.fields;
        Object.assign(describe, describeExt);
        describe.fields = fields;
      }
      return describe;
  }

  async formatDataAsync(rq: any) {
    if (
      (window as any).$dht.config.simpleCpq.isEnable &&
      (window as any).$dht.config.sail.isSimpleCpqShowAdjustPrice
    ) {
      // 显示固定搭配调整价
      let bomService = new BomService();
      await bomService.adjustSimpleCpqPrice(rq.data);
    }
    // 处理多单位
    let unitInstance = UnitService.getInstance();
    return unitInstance.formatDataAsync(rq);
  }

  formatCommodity(data: any) {
    const { dataList = [], objectDescribe } = data;
    // 如果描述存在，则获取单位和标签信息
    if (objectDescribe) {
      const commodityField = objectDescribe.fields.commodity_label;
      if (commodityField) {
        this.commodityOptions = commodityField.options;
      }
    }

    dataList.forEach((item: any) => {
      item.commodityOptions = this.getCommodityOptions(item);
    });
    return dataList;
  }

  /**
   * 商品新增几个字段
   * @param list
   * @return {any}
   */
  convertSpuList(list: any[]) {
    list.forEach((item: any) => {
      if (item.object_describe_api_name === 'SPUObj' && !item.is_spec) {
        const product = item.product_id__ro;
        item.product_id = product._id;
      }
    });
    return list;
  }

  getCommodityOptions(product: any) {
    const options: any[] = [];
    (product.commodity_label || []).forEach((item: any) => {
      const temp = this.commodityOptions.find((option: { value: any; }) => option.value === item);
      if (temp) {
        options.push(temp);
      }
    });
    return options;
  }

  collectChange(data: any) {
    let targetObj =this.listData.find(item => item._id === data.id);
    targetObj && (targetObj.is_in_collection = data.isCollect);
  }
}
</script>

<template src="./shop-list.html"></template>
<style src="./shop-list.less" lang="less"></style>
