.left-filter {
  width: 100%;
  background: #fff;
  border-radius: 4px;
  
  .filter-section {
    border-bottom: 1px solid #EAEBEE;
    margin-bottom: 24px;
    padding-bottom: 16px;
    
    &:last-child {
      border-bottom: none;
    }
    
    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px 16px 0;
      
      cursor: pointer;
      
      .filter-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }
      
      .expand-icon {
        font-size: 12px;
        color: #999;
        transition: transform 0.3s;
        user-select: none;
        
        &.expanded {
          transform: rotate(180deg);
        }
      }
    }
    
    .filter-content {
      padding: 0 16px 8px 0;
      box-sizing: border-box;
      
      .filter-option {
        display: block;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        /deep/ .fx-radio__label,
        /deep/ .fx-checkbox__label {
          font-size: 13px;
          color: #666;
          line-height: 1.5;
        }
        
        /deep/ .fx-radio__input.is-checked .fx-radio__inner,
        /deep/ .fx-checkbox__input.is-checked .fx-checkbox__inner {
          background-color: #409eff;
          border-color: #409eff;
        }
      }
      
      .filter-input {
        width: 100%;
        
        /deep/ .fx-input__inner {
          font-size: 13px;
          border-radius: 4px;
          border: 1px solid #dcdfe6;
          
          &:focus {
            border-color: #409eff;
          }
        }
      }
    }
  }
  
  // 单选组和多选组的样式
  /deep/ .fx-radio-group,
  /deep/ .fx-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  // 单选和多选项的样式
  /deep/ .fx-radio,
  /deep/ .fx-checkbox {
    margin-right: 0;
    margin-bottom: 0;
    
    .fx-radio__label,
    .fx-checkbox__label {
      padding-left: 8px;
      font-size: 13px;
      color: #666;
      line-height: 1.5;
    }
  }
  
  // 选中状态的样式
  /deep/ .fx-radio__input.is-checked + .fx-radio__label,
  /deep/ .fx-checkbox__input.is-checked + .fx-checkbox__label {
    color: #409eff;
    font-weight: 500;
  }
}